<?php

namespace App\Http\Requests\Taisan;

use Illuminate\Foundation\Http\FormRequest;

class ImportCongdapRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:xlsx,xls,csv',
                'max:10240', // 10MB max file size
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'file.required' => 'Vui lòng chọn file để import.',
            'file.file' => 'File upload không hợp lệ.',
            'file.mimes' => 'File phải có định dạng Excel (.xlsx, .xls) hoặc CSV.',
            'file.max' => 'Kích thước file không được vượt quá 10MB.',
        ];
    }
}
