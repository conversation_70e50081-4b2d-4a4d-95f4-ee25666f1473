<?php

namespace App\Http\Controllers\API\Taisan;

use App\Http\Controllers\Controller;
use App\Http\Requests\Taisan\StoreCongdapRequest;
use App\Http\Requests\Taisan\UpdateCongdapRequest;
use App\Http\Requests\Taisan\ImportCongdapRequest;
use App\Http\Resources\Taisan\CongdapResource;
use App\Http\Resources\Taisan\CongdapGeometryResource;
use App\Http\Resources\Taisan\CongdapAttributesResource;
use App\Services\CongdapService;
use App\Services\CongdapImportService;
use App\Traits\ApiResponseTrait;
use App\Exceptions\Taisan\CongdapNotFoundException;
use App\Exceptions\Taisan\InvalidGeometryException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Throwable;
use Illuminate\Support\Facades\Gate;

class CongdapController extends Controller
{
    use ApiResponseTrait;

    protected $congdapService;
    protected $importService;

    public function __construct(CongdapService $congdapService, CongdapImportService $importService)
    {
        $this->congdapService = $congdapService;
        $this->importService = $importService;
        // --- Áp dụng Middleware để kiểm tra quyền CHUNG ---
        // Cách này hiệu quả cho việc kiểm tra "User có quyền thực hiện hành động này nói chung không?"

        /* // Quyền xem (index, show, geometry, attributes)
        $this->middleware('permission:view_data')->only(['index', 'show', 'geometry', 'attributes']);

        // Quyền tạo (store)
        $this->middleware('permission:create_data')->only(['store']);

        // Quyền sửa (update)
        $this->middleware('permission:edit_data')->only(['update']);

        // Quyền xóa (destroy)
        $this->middleware('permission:delete_data')->only(['destroy']);
 */
        /*
        // --- Chuẩn bị cho Bước 2: Sử dụng Policies (Khi cần kiểm tra trên đối tượng cụ thể) ---
        // Khi bạn cần kiểm tra quyền trên từng Congdap cụ thể, bạn sẽ:
        // 1. Tạo một Policy: `php artisan make:policy CongdapPolicy --model=Taisan\\Congdap`
        // 2. Đăng ký Policy trong `AuthServiceProvider`.
        // 3. Thay thế hoặc bổ sung middleware bằng cách gọi `$this->authorize()` trong các phương thức controller.

        // Ví dụ (sẽ dùng sau này khi có Policy):
        // $this->authorizeResource(Congdap::class, 'congdap'); // Áp dụng policy cho các action chuẩn
        // Hoặc gọi trong từng phương thức:
        // public function show(Congdap $congdap) { // Sử dụng Route Model Binding
        //     $this->authorize('view', $congdap); // Kiểm tra quyền 'view' trên $congdap cụ thể
        //     // ...
        // }
        // public function update(UpdateCongdapRequest $request, Congdap $congdap) {
        //     $this->authorize('update', $congdap); // Kiểm tra quyền 'update' trên $congdap cụ thể
        //     // ...
        // }
        */
    }

    public function index()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->congdapService->getList($filters, $perPage);

            return $this->paginatedGeoJsonResponse(
                paginator: $paginator,
                resource: CongdapResource::class,
                message: 'Lấy danh sách cống đập thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching Congdap list: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách cống đập', 500);
        }
    }

    public function store(StoreCongdapRequest $request)
    {
        try {
            $congdap = $this->congdapService->create($request->validated());

            // Xóa cache có chủ đích sau khi tạo thành công
            $this->clearCongdapCache();

            return $this->createdResponse(
                data: new CongdapResource($congdap),
                message: 'Tạo cống đập thành công'
            );

        } catch (InvalidGeometryException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (Throwable $e) {
            Log::error('Error creating Congdap: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo cống đập', 500);
        }
    }

    public function show(string $id)
    {
        try {
            $congdap = $this->congdapService->getById($id);

            return $this->successResponse(
                data: new CongdapResource($congdap),
                message: 'Lấy thông tin cống đập thành công'
            );

        } catch (CongdapNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error fetching Congdap ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thông tin cống đập', 500);
        }
    }

    public function update(UpdateCongdapRequest $request, string $id)
    {
        try {
            $congdap = $this->congdapService->update($id, $request->validated());

            // Xóa cache có chủ đích sau khi cập nhật thành công
            $this->clearCongdapCache();

            return $this->updatedResponse(
                data: new CongdapResource($congdap),
                message: 'Cập nhật cống đập thành công'
            );

        } catch (CongdapNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (InvalidGeometryException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (Throwable $e) {
            Log::error('Error updating Congdap ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi cập nhật cống đập', 500);
        }
    }

    public function destroy(string $id)
    {
        try {
            $this->congdapService->delete($id);

            // Xóa cache có chủ đích sau khi xóa thành công
            $this->clearCongdapCache();

            return $this->deletedResponse('Xóa cống đập thành công');

        } catch (CongdapNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error deleting Congdap ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi xóa cống đập', 500);
        }
    }

    /**
     * Lấy danh sách chỉ bao gồm geometry (không có thuộc tính)
     * Tối ưu tài nguyên khi chỉ cần hiển thị dữ liệu không gian
     */
    public function geometry()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $paginator = $this->congdapService->getGeometryList($filters);

            return response()->json([
                'success' => true,
                'message' => 'Lấy dữ liệu geometry cống đập thành công',
                'data' => [
                    'type' => 'FeatureCollection',
                    'features' => CongdapGeometryResource::collection($paginator->items())
                ]
            ]);

        } catch (Throwable $e) {
            Log::error('Error fetching Congdap geometry: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy dữ liệu geometry cống đập', 500);
        }
    }

    /**
     * Lấy danh sách chỉ bao gồm thuộc tính (không có geometry)
     * Tối ưu tài nguyên khi chỉ cần hiển thị thông tin thuộc tính
     */
    public function attributes()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->congdapService->getAttributesList($filters, $perPage);

            return $this->paginatedResponse(
                paginator: $paginator,
                resource: CongdapAttributesResource::class,
                message: 'Lấy dữ liệu thuộc tính cống đập thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching Congdap attributes: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy dữ liệu thuộc tính cống đập', 500);
        }
    }

    /**
     * Import Excel data into congdap table
     */
    public function import(ImportCongdapRequest $request)
    {
        try {
            $file = $request->file('file');

            $results = $this->importService->import($file);

            if ($results['successful_count'] > 0) {
                return $this->successResponse(
                    data: $results,
                    message: "Import thành công! Đã nhập {$results['successful_count']} bản ghi."
                );
            } else {
                return $this->errorResponse(
                    'Import thất bại. Không có bản ghi nào được nhập thành công.',
                    422,
                    $results
                );
            }

        } catch (Throwable $e) {
            Log::error('Error importing Congdap data: ' . $e->getMessage(), [
                'file' => $request->file('file')?->getClientOriginalName(),
                'exception' => $e
            ]);

            return $this->errorResponse(
                'Lỗi hệ thống khi import dữ liệu: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Generate and download Excel template for congdap import
     */
    public function template()
    {
        try {
            $templatePath = $this->importService->generateTemplate();

            if (file_exists($templatePath)) {
                return response()->download($templatePath, 'congdap_import_template.xlsx');
            } else {
                return $this->errorResponse('Không thể tạo file template', 500);
            }

        } catch (Throwable $e) {
            Log::error('Error generating Congdap template: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo file template', 500);
        }
    }

    /**
     * Xóa cache có chủ đích cho dữ liệu cống đập
     */
    protected function clearCongdapCache(): void
    {
        $cacheTag = 'congdap-list';

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            Cache::tags([$cacheTag])->flush();
        } else {
            // Fallback: tăng cache version để invalidate tất cả cache
            $versionKey = 'congdap_version';
            $currentVersion = Cache::get($versionKey, 0);
            Cache::put($versionKey, $currentVersion + 1, 7200); // 2 hours
        }
    }
}
