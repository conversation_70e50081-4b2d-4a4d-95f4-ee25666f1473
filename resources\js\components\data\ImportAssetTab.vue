<template>
  <div class="bg-white px-6 py-4 rounded-lg shadow-sm border border-gray-200">
    <h2 class="text-lg font-semibold mb-4">Nhậ<PERSON> <PERSON><PERSON></h2>

    <!-- Trạng thái <PERSON> -->
    <div v-if="isLoading" class="flex items-center justify-center py-10">
      <svg class="w-8 h-8 mr-3 -ml-1 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="text-gray-600"><PERSON><PERSON> xử lý...</span>
    </div>

    <!-- Trạng thái Lỗi -->
    <div v-else-if="error" class="px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
      <strong class="font-bold">Lỗi!</strong>
      <span class="block sm:inline"> {{ error }}</span>
    </div>

    <!-- Form upload -->
    <div v-else class="space-y-4">
      <!-- Vùng kéo thả file -->
      <div  v-if="!selectedFile"
        class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors cursor-pointer"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
        :class="{ 'border-blue-500 bg-blue-50': isDragging }"
        @click="$refs.fileInput.click()"
      >
        <div class="space-y-2">
          <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <div class="text-sm text-gray-600">
            <label for="file-upload" class="relative cursor-pointer">
              <span class="font-medium text-blue-600 hover:text-blue-500">Chọn tệp để tải lên</span>
              <span class="text-gray-500"> hoặc kéo thả vào đây</span>
            </label>
          </div>
          <p class="text-xs text-gray-500">
            XLSX, XLS hoặc CSV
          </p>
        </div>
        <input 
          ref="fileInput"
          type="file" 
          class="hidden"
          accept=".xlsx,.xls,.csv"
          @change="handleFileSelect"
        >
      </div>

      <!-- Hiển thị file đã chọn -->
      <div v-if="selectedFile" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div class="flex items-center space-x-3">
          <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span class="text-sm text-gray-600">{{ selectedFile.name }}</span>
        </div>
        <button 
          @click="removeFile" 
          class="text-sm text-red-600 hover:text-red-800"
        >
          Xóa
        </button>
      </div>

      <!-- Hiển thị bảng xem trước dữ liệu -->
      <div v-if="isPreview && parsedData.length" class="mt-4">
        <h3 class="font-semibold mb-2">Xem trước dữ liệu</h3>
        <DataTable :columnDefs="columns" :rowData="parsedData" :height="'300px'" />
        <div v-if="validationErrors.length" class="mt-2 text-red-600">
          <div v-for="err in validationErrors" :key="err">{{ err }}</div>
        </div>
      </div>

      <!-- Nút xác nhận nhập -->
      <div v-if="isPreview && parsedData.length && !validationErrors.length" class="flex justify-end mt-2">
        <button @click="uploadFile" :disabled="isSubmitting" class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded hover:bg-green-700 disabled:opacity-50">
          <span v-if="isSubmitting">Đang nhập...</span>
          <span v-else>Xác nhận nhập</span>
        </button>
      </div>

      <!-- Nút Import -->
      <div class="flex justify-end space-x-3">
        <button
          @click="downloadTemplate"
          type="button"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Tải dữ liệu mẫu
        </button>
        <button
          @click="uploadFile"
          type="button"
          :disabled="!selectedFile || isSubmitting"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isSubmitting" class="flex items-center">
            <svg class="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg> 
            Đang import...
          </span>
          <span v-else>Nhập Dữ Liệu</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import axios from 'axios';
import * as XLSX from 'xlsx';
import DataTable from '@/components/DataTable.vue';

// Define Emits để thông báo cho parent component
const emit = defineEmits(['refresh-data'])

// State
const isLoading = ref(false);
const error = ref<string | null>(null);
const isSubmitting = ref(false);
const isDragging = ref(false);
const fileInput = ref<HTMLInputElement | null>(null);
const selectedFile = ref<File | null>(null);

// Dữ liệu xem trước và xác thực
const parsedData = ref<any[]>([]);
const columns = ref<any[]>([]);
const validationErrors = ref<string[]>([]);
const isPreview = ref(false);

// Xử lý kéo thả file
const handleDragOver = (event: DragEvent) => {
  isDragging.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  isDragging.value = false;
};

const handleDrop = (event: DragEvent) => {
  isDragging.value = false;
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    validateAndSetFile(files[0]);
  }
};

// Xử lý chọn file
const handleFileSelect = (event: Event) => {
  const files = (event.target as HTMLInputElement).files;
  if (files && files.length > 0) {
    validateAndSetFile(files[0]);
  }
};

// Validate và set file
const validateAndSetFile = (file: File) => {
  // Reset error
  error.value = null;
  
  // Kiểm tra định dạng file
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
  ];
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  
  if (!allowedTypes.includes(file.type) && !['xlsx', 'xls', 'csv'].includes(fileExtension || '')) {
    error.value = 'Chỉ chấp nhận file Excel (.xlsx, .xls) hoặc CSV';
    return;
  }

  // Kiểm tra kích thước file (10MB)
  if (file.size > 10 * 1024 * 1024) {
    error.value = 'Kích thước file không được vượt quá 10MB';
    return;
  }

  selectedFile.value = file;
  parseFile(file);
};

// Phân tích file Excel/CSV
const parseFile = (file: File) => {
  isLoading.value = true;
  error.value = null;
  parsedData.value = [];
  columns.value = [];
  validationErrors.value = [];
  isPreview.value = false;

  const reader = new FileReader();
  reader.onload = (e: any) => {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const json = XLSX.utils.sheet_to_json(worksheet, { defval: '' });
      if (!json.length) throw new Error('File không có dữ liệu');
      parsedData.value = json;
      columns.value = Object.keys(json[0]).map(key => ({ headerName: key, field: key }));
      validateParsedData();
      isPreview.value = true;
    } catch (err: any) {
      error.value = 'Không thể đọc file hoặc định dạng không hợp lệ.';
      parsedData.value = [];
      columns.value = [];
      isPreview.value = false;
    } finally {
      isLoading.value = false;
    }
  };
  reader.onerror = () => {
    error.value = 'Lỗi khi đọc file.';
    isLoading.value = false;
  };
  reader.readAsArrayBuffer(file);
};

// Xác thực dữ liệu đã phân tích
const validateParsedData = () => {
  validationErrors.value = [];
  if (!parsedData.value.length) return;
  // Ví dụ: kiểm tra cột bắt buộc
  // Cập nhật các trường bắt buộc để khớp với file mẫu của bạn.
  const requiredFields = ['Tên công trình', 'Mã xã'];
  parsedData.value.forEach((row, idx) => {
    requiredFields.forEach(field => {
      if (row[field] === undefined || row[field] === null || row[field].toString().trim() === '') {
        validationErrors.value.push(`Dòng ${idx + 2}: Thiếu dữ liệu cho cột bắt buộc "${field}"`);
      }
    });
  });
};

// Xóa file đã chọn
const removeFile = () => {
  selectedFile.value = null;
  parsedData.value = [];
  columns.value = [];
  validationErrors.value = [];
  isPreview.value = false;
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// Tải file mẫu
const downloadTemplate = async () => {
  try {
    const response = await axios.get('/api/taisan/congdap/template', {
      responseType: 'blob'
    });
    
    // Tạo URL cho blob
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'DanhSachTaiSan.xlsx');
    document.body.appendChild(link);
    link.click();
    
    // Cleanup
    link.parentNode?.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (err: any) {
    console.error('Lỗi khi tải file mẫu:', err);
    error.value = 'Có lỗi xảy ra khi tải file mẫu. Vui lòng thử lại.';
  }
};

// Upload file (chỉ gửi khi đã xác nhận preview)
const uploadFile = async () => {
  if (!selectedFile.value) {
    error.value = 'Vui lòng chọn file để import';
    return;
  }
  if (!isPreview.value || validationErrors.value.length) {
    error.value = 'Vui lòng kiểm tra và xác nhận dữ liệu trước khi nhập.';
    return;
  }

  try {
    isSubmitting.value = true;
    error.value = null;

    const formData = new FormData();
    formData.append('file', selectedFile.value);

    const response = await axios.post('/api/taisan/congdap/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response.data.success) {
      // Reset form
      removeFile();
      // Trigger refresh data
      emit('refresh-data');
      // Show success message
      alert('Nhập dữ liệu thành công!');
    } else {
      throw new Error(response.data.message || 'Nhập thất bại');
    }
  } catch (err: any) {
    console.error('Lỗi khi import:', err);
    error.value = err.response?.data?.message || err.message || 'Có lỗi xảy ra khi import dữ liệu. Vui lòng thử lại.';
  } finally {
    isSubmitting.value = false;
  }
};
</script>
