<?php

namespace App\Services;

use App\Models\API\Taisan\Congdap;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Throwable;

class CongdapImportService
{
    protected $batchSize = 100;
    protected $cacheTag = 'congdap-list';

    public function __construct()
    {
        // Constructor for the import service
    }

    /**
     * Import Excel file data into congdap table
     */
    public function import(UploadedFile $file): array
    {
        try {
            DB::beginTransaction();

            $import = new CongdapExcelImport();
            Excel::import($import, $file);

            $results = $import->getResults();
            
            if ($results['successful_count'] > 0) {
                // Clear cache after successful import
                Cache::tags([$this->cacheTag])->flush();
                DB::commit();
                
                Log::info('Congdap import completed', [
                    'total_rows' => $results['total_rows'],
                    'successful' => $results['successful_count'],
                    'failed' => $results['failed_count']
                ]);
            } else {
                DB::rollBack();
            }

            return $results;

        } catch (Throwable $e) {
            DB::rollBack();
            Log::error('Congdap import failed: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'exception' => $e
            ]);

            return [
                'total_rows' => 0,
                'successful_count' => 0,
                'failed_count' => 0,
                'errors' => [
                    [
                        'row' => 0,
                        'errors' => ['Lỗi hệ thống: ' . $e->getMessage()]
                    ]
                ]
            ];
        }
    }

    /**
     * Generate Excel template for congdap import
     */
    public function generateTemplate(): string
    {
        $headers = [
            'id_qt',
            'id_xa',
            'ten',
            'quymo_ct',
            'loai_ct',
            'nam_xd',
            'nam_sd',
            'dt_dat',
            'tinhtrang',
            'quytrinh_vh',
            'quytrinh_bt',
            'dv_quanly',
            'phuongthuc',
            'chuthich',
            'longitude',
            'latitude'
        ];

        $descriptions = [
            'Mã quyết toán',
            'Mã xã',
            'Tên công trình',
            'Quy mô công trình',
            'Loại công trình',
            'Năm xây dựng',
            'Năm sử dụng',
            'Diện tích đất',
            'Tình trạng',
            'Quy trình vận hành',
            'Quy trình bảo trì',
            'Đơn vị quản lý',
            'Phương thức',
            'Chú thích',
            'Kinh độ (tùy chọn)',
            'Vĩ độ (tùy chọn)'
        ];

        $templatePath = storage_path('app/templates/congdap_import_template.xlsx');

        // Create template directory if it doesn't exist
        if (!file_exists(dirname($templatePath))) {
            mkdir(dirname($templatePath), 0755, true);
        }

        // Create simple Excel template
        $export = new CongdapTemplateExport($headers, $descriptions);
        Excel::store($export, 'templates/congdap_import_template.xlsx');

        return $templatePath;
    }
}

/**
 * Excel Import Class for Congdap
 */
class CongdapExcelImport implements ToArray, WithHeadingRow
{

    protected $results = [
        'total_rows' => 0,
        'successful_count' => 0,
        'failed_count' => 0,
        'errors' => []
    ];

    protected $batchSize = 100;
    protected $currentBatch = [];

    public function array(array $rows): void
    {
        $this->results['total_rows'] = count($rows);

        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2; // +2 because Excel starts at 1 and we have header row
            
            try {
                $validatedData = $this->validateAndTransformRow($row, $rowNumber);
                
                if ($validatedData) {
                    $this->currentBatch[] = $validatedData;
                    
                    // Process batch when it reaches the batch size
                    if (count($this->currentBatch) >= $this->batchSize) {
                        $this->processBatch();
                    }
                }
            } catch (Throwable $e) {
                $this->results['failed_count']++;
                $this->results['errors'][] = [
                    'row' => $rowNumber,
                    'errors' => [$e->getMessage()]
                ];
            }
        }

        // Process remaining items in batch
        if (!empty($this->currentBatch)) {
            $this->processBatch();
        }
    }

    protected function validateAndTransformRow(array $row, int $rowNumber): ?array
    {
        $errors = [];

        // Clean and validate data
        $data = [
            'id_qt' => $this->cleanString($row['id_qt'] ?? null, 20),
            'id_xa' => $this->cleanString($row['id_xa'] ?? null, 5),
            'ten' => $this->cleanString($row['ten'] ?? null, 100),
            'quymo_ct' => $this->cleanString($row['quymo_ct'] ?? null, 100),
            'loai_ct' => $this->cleanString($row['loai_ct'] ?? null, 5),
            'nam_xd' => $this->cleanInteger($row['nam_xd'] ?? null),
            'nam_sd' => $this->cleanInteger($row['nam_sd'] ?? null),
            'dt_dat' => $this->cleanFloat($row['dt_dat'] ?? null),
            'tinhtrang' => $this->cleanString($row['tinhtrang'] ?? null, 15),
            'quytrinh_vh' => $this->cleanString($row['quytrinh_vh'] ?? null, 50),
            'quytrinh_bt' => $this->cleanString($row['quytrinh_bt'] ?? null, 50),
            'dv_quanly' => $this->cleanString($row['dv_quanly'] ?? null, 50),
            'phuongthuc' => $this->cleanString($row['phuongthuc'] ?? null, 100),
            'chuthich' => $this->cleanString($row['chuthich'] ?? null),
        ];

        // Handle geometry if coordinates are provided
        $longitude = $this->cleanFloat($row['longitude'] ?? null);
        $latitude = $this->cleanFloat($row['latitude'] ?? null);
        
        if ($longitude !== null && $latitude !== null) {
            if ($longitude >= -180 && $longitude <= 180 && $latitude >= -90 && $latitude <= 90) {
                $data['geom'] = Congdap::geometryFromGeoJSON(json_encode([
                    'type' => 'Point',
                    'coordinates' => [$longitude, $latitude]
                ]));
            } else {
                $errors[] = 'Tọa độ không hợp lệ (longitude: -180 đến 180, latitude: -90 đến 90)';
            }
        }

        // Validate required fields if needed
        if (empty($data['ten'])) {
            $errors[] = 'Tên công trình không được để trống';
        }

        if (!empty($errors)) {
            $this->results['failed_count']++;
            $this->results['errors'][] = [
                'row' => $rowNumber,
                'errors' => $errors
            ];
            return null;
        }

        return $data;
    }

    protected function processBatch(): void
    {
        try {
            foreach ($this->currentBatch as $data) {
                Congdap::create($data);
                $this->results['successful_count']++;
            }
        } catch (Throwable $e) {
            $this->results['failed_count'] += count($this->currentBatch);
            $this->results['errors'][] = [
                'row' => 'batch',
                'errors' => ['Lỗi xử lý batch: ' . $e->getMessage()]
            ];
        }

        $this->currentBatch = [];
    }

    protected function cleanString(?string $value, ?int $maxLength = null): ?string
    {
        if ($value === null || trim($value) === '') {
            return null;
        }
        
        $cleaned = trim($value);
        
        if ($maxLength && strlen($cleaned) > $maxLength) {
            $cleaned = substr($cleaned, 0, $maxLength);
        }
        
        return $cleaned;
    }

    protected function cleanInteger($value): ?int
    {
        if ($value === null || $value === '') {
            return null;
        }
        
        return is_numeric($value) ? (int) $value : null;
    }

    protected function cleanFloat($value): ?float
    {
        if ($value === null || $value === '') {
            return null;
        }
        
        return is_numeric($value) ? (float) $value : null;
    }

    public function getResults(): array
    {
        return $this->results;
    }

    public function onError(Throwable $error): void
    {
        Log::error('Excel import error: ' . $error->getMessage());
    }

    public function onFailure(Failure ...$failures): void
    {
        foreach ($failures as $failure) {
            $this->results['failed_count']++;
            $this->results['errors'][] = [
                'row' => $failure->row(),
                'errors' => $failure->errors()
            ];
        }
    }
}

/**
 * Excel Template Export Class
 */
class CongdapTemplateExport implements \Maatwebsite\Excel\Concerns\FromArray
{
    protected $headers;
    protected $descriptions;

    public function __construct(array $headers, array $descriptions)
    {
        $this->headers = $headers;
        $this->descriptions = $descriptions;
    }

    public function array(): array
    {
        return [
            $this->headers, // Column names
            $this->descriptions, // Column descriptions
        ];
    }
}
