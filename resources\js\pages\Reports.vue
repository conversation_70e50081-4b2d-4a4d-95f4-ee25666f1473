<template>
  <MainLayout>
    <template #sidebar>
      <div class="p-4">
        <div class="flex flex-col gap-2">
          <!--  <button class="control-btn">T<PERSON>o báo cáo mới</button>
          <button class="control-btn"><PERSON><PERSON><PERSON> b<PERSON>o cáo</button> -->
          <button
            class="text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md transition-colors duration-200 ease-in-out text-sm"
            :class="{
              'text-blue-600 bg-blue-50': activeView === 'export'
            }"
            @click="setActiveView('export')">
            Xu<PERSON>t báo cáo theo mẫu
          </button>
          <button
            class="text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md transition-colors duration-200 ease-in-out text-sm"
            :class="{
              'text-blue-600 bg-blue-50': activeView === 'stats'
            }"
            @click="setActiveView('stats')">
            <PERSON><PERSON><PERSON> bi<PERSON><PERSON> đồ thống kê
          </button>
          <button
            class="text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md transition-colors duration-200 ease-in-out text-sm"
            :class="{
              'text-blue-600 bg-blue-50': activeView === 'depreciation'
            }"
            @click="setActiveView('depreciation')">
            Tính giá trị hao mòn
          </button>
        </div>
      </div>
    </template>

    <div class="h-full flex flex-col">
      <!-- View xuất báo cáo thống kê -->
      <ExportReport v-if="activeView === 'export'" />
      <ReportStats v-else-if="activeView === 'stats'" reportTypeName="Biểu đồ thống kê tài sản" />
      <!-- View tính giá trị hao mòn -->
      <DepreciationCalculator v-else-if="activeView === 'depreciation'" />
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import ExportReport from '@/components/reports/ExportReport.vue'
import DepreciationCalculator from '@/components/reports/DepreciationCalculator.vue'
import ReportStats from '@/components/reports/ReportStats.vue'

// Biến lưu trạng thái view đang hiển thị
// 'export': View xuất báo cáo thống kê (mặc định)
// 'depreciation': View tính giá trị hao mòn
const activeView = ref('export')

// Hàm chuyển đổi giữa các view
function setActiveView(view: string) {
  activeView.value = view
}
</script>